import React from "react";
import { Path, Svg } from "react-native-svg";
import { Colors } from "../constants/Colors";

interface IconProps {
  width?: number;
  height?: number;
  color?: string;
}

export const MenuIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.text,
}) => (
  <Svg width={width} height={height} viewBox="0 0 18 14" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18 7C18 7.41421 17.6642 7.75 17.25 7.75H0.75C0.335786 7.75 0 7.41421 0 7C0 6.58579 0.335786 6.25 0.75 6.25H17.25C17.6642 6.25 18 6.58579 18 7ZM0.75 1.75H17.25C17.6642 1.75 18 1.41421 18 1C18 0.585786 17.6642 0.25 17.25 0.25H0.75C0.335786 0.25 0 0.585786 0 1C0 1.41421 0.335786 1.75 0.75 1.75ZM17.25 12.25H0.75C0.335786 12.25 0 12.5858 0 13C0 13.4142 0.335786 13.75 0.75 13.75H17.25C17.6642 13.75 18 13.4142 18 13C18 12.5858 17.6642 12.25 17.25 12.25Z"
      fill={color}
    />
  </Svg>
);

export const PhoneIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.text,
}) => (
  <Svg width={width} height={height} viewBox="0 0 19 19" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.8472 12.8556L13.4306 10.8766L13.4184 10.8709C12.9526 10.6717 12.4177 10.7211 11.9963 11.0022C11.9718 11.0183 11.9483 11.0359 11.9259 11.0547L9.64406 13C8.19844 12.2978 6.70594 10.8166 6.00375 9.38969L7.95187 7.07312C7.97062 7.04969 7.98844 7.02625 8.00531 7.00094C8.28032 6.5807 8.32677 6.05073 8.12906 5.58906V5.57781L6.14437 1.15375C5.88009 0.543904 5.246 0.180692 4.58625 0.26125C1.95833 0.607054 -0.00475144 2.84943 0 5.5C0 12.9438 6.05625 19 13.5 19C16.1506 19.0048 18.3929 17.0417 18.7388 14.4137C18.8195 13.7542 18.4567 13.1202 17.8472 12.8556Z"
      fill={color}
    />
  </Svg>
);

export const ChartIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.text,
}) => (
  <Svg width={width} height={height} viewBox="0 0 20 18" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.75 16.5C19.75 16.9142 19.4142 17.25 19 17.25H1C0.585786 17.25 0.25 16.9142 0.25 16.5V1.5C0.25 1.08579 0.585786 0.75 1 0.75C1.41421 0.75 1.75 1.08579 1.75 1.5V10.3472L6.50594 6.1875C6.77266 5.95401 7.16644 5.93915 7.45 6.15187L12.9634 10.2872L18.5059 5.4375C18.704 5.24149 18.9943 5.1714 19.2599 5.2555C19.5256 5.33959 19.7227 5.56402 19.7718 5.83828C19.8209 6.11254 19.714 6.39143 19.4941 6.5625L13.4941 11.8125C13.2273 12.046 12.8336 12.0608 12.55 11.8481L7.03656 7.71469L1.75 12.3403V15.75H19C19.4142 15.75 19.75 16.0858 19.75 16.5Z"
      fill={color}
    />
  </Svg>
);

export const BookIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.text,
}) => (
  <Svg width={width} height={height} viewBox="0 0 22 19" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20 0.5H14C12.8197 0.5 11.7082 1.05573 11 2C10.2918 1.05573 9.18034 0.5 8 0.5H2C1.17157 0.5 0.5 1.17157 0.5 2V14C0.5 14.8284 1.17157 15.5 2 15.5H8C9.24264 15.5 10.25 16.5074 10.25 17.75C10.25 18.1642 10.5858 18.5 11 18.5C11.4142 18.5 11.75 18.1642 11.75 17.75C11.75 16.5074 12.7574 15.5 14 15.5H20C20.8284 15.5 21.5 14.8284 21.5 14V2C21.5 1.17157 20.8284 0.5 20 0.5Z"
      fill={color}
    />
  </Svg>
);

export const SearchIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.text,
}) => (
  <Svg width={width} height={height} viewBox="0 0 20 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.5306 18.4694L14.8366 13.7762C17.6629 10.383 17.3204 5.36693 14.0591 2.38935C10.7978 -0.588237 5.77134 -0.474001 2.64867 2.64867C-0.474001 5.77134 -0.588237 10.7978 2.38935 14.0591C5.36693 17.3204 10.383 17.6629 13.7762 14.8366L18.4694 19.5306C18.7624 19.8237 19.2376 19.8237 19.5306 19.5306C19.8237 19.2376 19.8237 18.7624 19.5306 18.4694Z"
      fill={color}
    />
  </Svg>
);

export const ClockIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.text,
}) => (
  <Svg width={width} height={height} viewBox="0 0 20 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 0.25C4.61522 0.25 0.25 4.61522 0.25 10C0.25 15.3848 4.61522 19.75 10 19.75C15.3848 19.75 19.75 15.3848 19.75 10C19.7443 4.61758 15.3824 0.255684 10 0.25Z"
      fill={color}
    />
  </Svg>
);

export const ShieldIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.text,
}) => (
  <Svg width={width} height={height} viewBox="0 0 18 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.5 0.75H1.5C0.671573 0.75 0 1.42157 0 2.25V7.76062C0 16.1616 7.10812 18.9487 8.53125 19.4222C8.8352 19.5256 9.1648 19.5256 9.46875 19.4222C10.8938 18.9487 18 16.1616 18 7.76062V2.25C18 1.42157 17.3284 0.75 16.5 0.75Z"
      fill={color}
    />
  </Svg>
);

export const BrainIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.text,
}) => (
  <Svg width={width} height={height} viewBox="0 0 24 19" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.25 9.625C23.248 7.59744 22.0808 5.75154 20.25 4.88031V4.75C20.2482 2.76581 18.9471 1.01696 17.0471 0.445025C15.1471 -0.126909 13.0968 0.613073 12 2.26656C10.9032 0.613073 8.85286 -0.126909 6.95289 0.445025C5.05292 1.01696 3.75178 2.76581 3.75 4.75V4.88031C1.91746 5.74923 0.749297 7.59595 0.749297 9.62406C0.749297 11.6522 1.91746 13.4989 3.75 14.3678V14.5C3.75178 16.4842 5.05292 18.233 6.95289 18.805C8.85286 19.3769 10.9032 18.6369 12 16.9834C13.0968 18.6369 15.1471 19.3769 17.0471 18.805C18.9471 18.233 20.2482 16.4842 20.25 14.5V14.3678C22.0805 13.4972 23.2477 11.652 23.25 9.625Z"
      fill={color}
    />
  </Svg>
);

export const HeartIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.text,
}) => (
  <Svg width={width} height={height} viewBox="0 0 22 18" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.6875 0C13.7516 0 12.0566 0.8325 11 2.23969C9.94344 0.8325 8.24844 0 6.3125 0C3.10384 0.00361657 0.503617 2.60384 0.5 5.8125C0.5 5.88281 0.5 5.95312 0.5 6.02344C0.512944 6.43765 0.859224 6.76294 1.27344 6.75C1.68765 6.73706 2.01294 6.39078 2 5.97656C2 5.92219 2 5.86687 2 5.8125C2.0031 3.43206 3.93206 1.5031 6.3125 1.5C8.13594 1.5 9.66687 2.47125 10.3062 4.03125C10.4218 4.31259 10.6959 4.49627 11 4.49627C11.3041 4.49627 11.5782 4.31259 11.6938 4.03125C12.3331 2.46844 13.8641 1.5 15.6875 1.5C18.0679 1.5031 19.9969 3.43206 20 5.8125C20 10.8384 12.71 15.3891 11 16.3875C9.98469 15.7959 7.00344 13.95 4.74875 11.4928C4.56755 11.2952 4.29469 11.2093 4.03295 11.2674C3.77121 11.3255 3.56036 11.5188 3.47983 11.7746C3.39929 12.0303 3.4613 12.3096 3.6425 12.5072C6.56469 15.6947 10.4797 17.8209 10.6447 17.91C10.8665 18.0293 11.1335 18.0293 11.3553 17.91C11.7697 17.6869 21.5 12.375 21.5 5.8125C21.4964 2.60384 18.8962 0.00361657 15.6875 0Z"
      fill={color}
    />
  </Svg>
);

export const GraduationIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.text,
}) => (
  <Svg width={width} height={height} viewBox="0 0 24 22" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.6025 6.33813L12.3525 0.338125C12.1321 0.220791 11.8679 0.220791 11.6475 0.338125L0.3975 6.33813C0.152987 6.46843 0.000234297 6.72293 0.000234297 7C0.000234297 7.27707 0.152987 7.53157 0.3975 7.66187L3 9.05031V13.5897C2.99922 13.9581 3.1348 14.3137 3.38063 14.5881C4.60875 15.9559 7.36031 18.25 12 18.25C13.5384 18.2627 15.0653 17.9841 16.5 17.4287V20.5C16.5 20.9142 16.8358 21.25 17.25 21.25C17.6642 21.25 18 20.9142 18 20.5V16.7041C18.978 16.1395 19.8618 15.4256 20.6194 14.5881C20.8652 14.3137 21.0008 13.9581 21 13.5897V9.05031L23.6025 7.66187C23.847 7.53157 23.9998 7.27707 23.9998 7C23.9998 6.72293 23.847 6.46843 23.6025 6.33813V6.28844Z"
      fill={color}
    />
  </Svg>
);

export const UserIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.text,
}) => (
  <Svg width={width} height={height} viewBox="0 0 20 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.6488 17.875C18.2209 15.4066 16.0206 13.6366 13.4528 12.7975C16.0635 11.2433 17.3141 8.13638 16.5082 5.2069C15.7022 2.27741 13.0383 0.247449 10 0.247449C6.96167 0.247449 4.29779 2.27741 3.49182 5.2069C2.68585 8.13638 3.93645 11.2433 6.54719 12.7975C3.97938 13.6356 1.77906 15.4056 0.35125 17.875C0.208704 18.1074 0.203527 18.3989 0.337731 18.6363C0.471935 18.8736 0.724375 19.0194 0.997024 19.0171C1.26967 19.0147 1.51958 18.8646 1.64969 18.625C3.41594 15.5725 6.53781 13.75 10 13.75C13.4622 13.75 16.5841 15.5725 18.3503 18.625C18.4804 18.8646 18.7303 19.0147 19.003 19.0171C19.2756 19.0194 19.5281 18.8736 19.6623 18.6363C19.7965 18.3989 19.7913 18.1074 19.6488 17.875Z"
      fill={color}
    />
  </Svg>
);

export const UsersIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.text,
}) => (
  <Svg width={width} height={height} viewBox="0 0 24 18" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M22.95 10.1C22.6186 10.3485 22.1485 10.2814 21.9 9.95C20.9833 8.71775 19.5358 7.99404 18 8C17.6985 7.99997 17.4263 7.81937 17.3091 7.54156C17.2304 7.35514 17.2304 7.14486 17.3091 6.95844C17.4263 6.68063 17.6985 6.50003 18 6.5C19.1692 6.4999 20.1435 5.60425 20.2418 4.43916C20.3401 3.27406 19.5297 2.22784 18.377 2.03184C17.2243 1.83584 16.1136 2.55539 15.8212 3.6875C15.7177 4.08877 15.3085 4.33012 14.9072 4.22656C14.5059 4.12301 14.2646 3.71377 14.3681 3.3125C14.7687 1.76266 16.1088 0.637938 17.7047 0.512366C19.3005 0.386793 20.8001 1.28805 21.4381 2.75618C22.0761 4.2243 21.7119 5.93555 20.5312 7.01656C21.5511 7.45812 22.4376 8.15882 23.1028 9.04906C23.2222 9.2086 23.2731 9.40907 23.2444 9.60624C23.2158 9.8034 23.1098 9.98106 22.95 10.1Z"
      fill={color}
    />
  </Svg>
);

export const StudentIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.text,
}) => (
  <Svg width={width} height={height} viewBox="0 0 20 19" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.2372 3.28844L10.2372 0.288438C10.0832 0.237113 9.91677 0.237113 9.76281 0.288438L0.762813 3.28844C0.456545 3.39053 0.249976 3.67716 0.25 4V11.5C0.25 11.9142 0.585786 12.25 1 12.25C1.41421 12.25 1.75 11.9142 1.75 11.5V5.04063L4.89906 6.08969C3.15695 8.9042 4.02321 12.5979 6.835 14.3444C5.1475 15.0063 3.68875 16.2034 2.62187 17.8403C2.47087 18.0647 2.45242 18.3531 2.5736 18.595C2.69477 18.8368 2.93679 18.9947 3.20697 19.0081C3.47714 19.0215 3.7336 18.8884 3.87813 18.6597C5.29094 16.4922 7.52219 15.25 10 15.25C12.4778 15.25 14.7091 16.4922 16.1219 18.6597C16.3508 18.9999 16.8104 19.0932 17.1539 18.8692C17.4973 18.6452 17.5972 18.1869 17.3781 17.8403C16.3112 16.2034 14.8469 15.0063 13.165 14.3444C15.9739 12.598 16.8399 8.9079 15.1009 6.09437L19.2372 4.71625C19.5435 4.61421 19.7502 4.32757 19.7502 4.00469C19.7502 3.68181 19.5435 3.39516 19.2372 3.29313V3.28844Z"
      fill={color}
    />
  </Svg>
);

export const TwitterIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.textSecondary,
}) => (
  <Svg width={width} height={height} viewBox="0 0 21 19" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.1928 3.46313C20.0768 3.18285 19.8033 3.00006 19.5 3H16.6472C15.8359 1.61972 14.3604 0.765791 12.7594 0.75C11.5747 0.734462 10.4339 1.19754 9.59531 2.03438C8.73219 2.88138 8.24717 4.04071 8.25 5.25V5.82094C4.47563 4.82531 1.38844 1.755 1.35563 1.72219C1.15019 1.51493 0.843182 1.44566 0.568648 1.54461C0.294115 1.64356 0.101905 1.89277 0.0759375 2.18344C-0.328125 6.66375 0.973125 9.66187 2.13844 11.3878C2.70664 12.241 3.39786 13.0055 4.18969 13.6566C2.76188 15.3 0.51375 16.1634 0.489375 16.1728C0.274975 16.2531 0.108995 16.4269 0.0386822 16.6448C-0.031631 16.8627 0.00142384 17.1008 0.128438 17.2913C0.19875 17.3962 0.48 17.7647 1.16719 18.1087C2.01656 18.5344 3.13875 18.75 4.5 18.75C11.1253 18.75 16.6612 13.6481 17.2266 7.08375L20.0306 4.28062C20.2451 4.06601 20.3091 3.74335 20.1928 3.46313Z"
      fill={color}
    />
  </Svg>
);

export const FacebookIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.textSecondary,
}) => (
  <Svg width={width} height={height} viewBox="0 0 20 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 0.25C4.61522 0.25 0.25 4.61522 0.25 10C0.25 15.3848 4.61522 19.75 10 19.75C15.3848 19.75 19.75 15.3848 19.75 10C19.7443 4.61758 15.3824 0.255684 10 0.25Z"
      fill={color}
    />
  </Svg>
);

export const InstagramIcon: React.FC<IconProps> = ({
  width = 24,
  height = 24,
  color = Colors.textSecondary,
}) => (
  <Svg width={width} height={height} viewBox="0 0 20 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 5.5C7.51472 5.5 5.5 7.51472 5.5 10C5.5 12.4853 7.51472 14.5 10 14.5C12.4853 14.5 14.5 12.4853 14.5 10C14.4974 7.51579 12.4842 5.50258 10 5.5Z"
      fill={color}
    />
  </Svg>
);

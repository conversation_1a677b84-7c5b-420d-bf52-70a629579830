import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { Colors } from "../constants/Colors";
import { Spacing } from "../constants/Spacing";
import { Typography } from "../constants/Typography";
import { FacebookIcon, InstagramIcon, TwitterIcon } from "./Icons";

export const Footer: React.FC = () => {
  const handleLinkPress = (link: string) => {
    console.log(`${link} pressed`);
  };

  const handleSocialPress = (platform: string) => {
    console.log(`${platform} pressed`);
  };

  return (
    <View style={styles.container}>
      <View style={styles.linksContainer}>
        <TouchableOpacity
          style={styles.linkItem}
          onPress={() => handleLinkPress("Services")}
          activeOpacity={0.7}
        >
          <Text style={styles.linkText}>Services</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.linkItem}
          onPress={() => handleLinkPress("About")}
          activeOpacity={0.7}
        >
          <Text style={styles.linkText}>About</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.linkItem}
          onPress={() => handleLinkPress("Contact")}
          activeOpacity={0.7}
        >
          <Text style={styles.linkText}>Contact</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.linkItem}
          onPress={() => handleLinkPress("Privacy Policy")}
          activeOpacity={0.7}
        >
          <Text style={styles.linkText}>Privacy Policy</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.linkItem}
          onPress={() => handleLinkPress("Terms of Service")}
          activeOpacity={0.7}
        >
          <Text style={styles.linkText}>Terms of Service</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.socialContainer}>
        <TouchableOpacity
          onPress={() => handleSocialPress("Twitter")}
          activeOpacity={0.7}
        >
          <TwitterIcon width={24} height={24} color={Colors.textSecondary} />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => handleSocialPress("Facebook")}
          activeOpacity={0.7}
        >
          <FacebookIcon width={24} height={24} color={Colors.textSecondary} />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => handleSocialPress("Instagram")}
          activeOpacity={0.7}
        >
          <InstagramIcon width={24} height={24} color={Colors.textSecondary} />
        </TouchableOpacity>
      </View>
      <View style={styles.copyrightContainer}>
        <Text style={styles.copyright}>
          © 2024 AZMed+. All rights reserved.
        </Text>
      </View>
      <View style={styles.spacer} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing["3xl"],
    backgroundColor: Colors.white,
  },
  linksContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: Spacing.xl,
    flexWrap: "wrap",
    marginBottom: Spacing.xl,
  },
  linkItem: {
    width: 160,
    minWidth: 160,
    alignItems: "center",
  },
  linkText: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.normal,
    lineHeight: 24,
    color: Colors.textSecondary,
    textAlign: "center",
  },
  socialContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "flex-start",
    gap: Spacing.base,
    flexWrap: "wrap",
    marginBottom: Spacing.xl,
  },
  copyrightContainer: {
    alignItems: "center",
  },
  copyright: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.normal,
    lineHeight: 24,
    color: Colors.textSecondary,
    textAlign: "center",
  },
  spacer: {
    height: 20,
    backgroundColor: Colors.white,
  },
});

import { LinearGradient } from "expo-linear-gradient";
import React from "react";
import {
    Dimensions,
    StyleSheet,
    Text,
    View
} from "react-native";
import { Colors } from "../constants/Colors";
import { Spacing } from "../constants/Spacing";
import { Typography } from "../constants/Typography";
import { Button } from "./Button";

const { width: screenWidth } = Dimensions.get("window");

export const HeroSection: React.FC = () => {
  const handleStartJourney = () => {
    // Handle start journey action
    console.log("Start Your Health Journey pressed");
  };

  const handleBookConsultation = () => {
    // Handle book consultation action
    console.log("Book Consultation pressed");
  };

  return (
    <View style={styles.heroContainer}>
      <LinearGradient
        colors={["rgba(0, 0, 0, 0.1)", "rgba(0, 0, 0, 0.4)"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradient}
      >
        <View style={styles.contentContainer}>
          <View style={styles.textContainer}>
            <View style={styles.titleContainer}>
              <Text style={styles.title}>
                Your Complete Women's Health Digital Companion
              </Text>
            </View>
            <Text style={styles.subtitle}>
              From pregnancy planning to menopause - AI-powered care, expert
              consultations, and personalized health tracking
            </Text>
          </View>
          <View style={styles.buttonContainer}>
            <Button
              title="Start Your Health Journey"
              onPress={handleStartJourney}
              variant="primary"
              style={styles.primaryButton}
            />
            <Button
              title="Book Consultation"
              onPress={handleBookConsultation}
              variant="secondary"
              style={styles.secondaryButton}
            />
          </View>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  heroContainer: {
    minHeight: 480,
    backgroundColor: "#5A8A9C", // Fallback color for the background
  },
  gradient: {
    flex: 1,
    paddingHorizontal: Spacing.base,
    paddingTop: 151,
    paddingBottom: 7,
    justifyContent: "flex-end",
  },
  contentContainer: {
    gap: Spacing.xl,
  },
  textContainer: {
    gap: Spacing.sm,
  },
  titleContainer: {
    height: 135,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize["3xl"],
    fontWeight: Typography.fontWeight.bold,
    lineHeight: 45,
    letterSpacing: -1,
    color: Colors.white,
    textAlign: "center",
  },
  subtitle: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.normal,
    lineHeight: 21,
    color: Colors.white,
    textAlign: "center",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "flex-start",
    gap: Spacing.md,
    flexWrap: "wrap",
  },
  primaryButton: {
    minWidth: 170,
  },
  secondaryButton: {
    minWidth: 130,
  },
});

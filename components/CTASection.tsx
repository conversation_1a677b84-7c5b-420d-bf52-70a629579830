import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { Colors } from "../constants/Colors";
import { Spacing } from "../constants/Spacing";
import { Typography } from "../constants/Typography";
import { Button } from "./Button";

export const CTASection: React.FC = () => {
  const handleGetStarted = () => {
    // Handle get started action
    console.log("Get Started Today pressed");
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.sectionTitle}>
          Ready to Transform Your Health Journey?
        </Text>
      </View>
      <View style={styles.buttonContainer}>
        <Button
          title="Get Started Today"
          onPress={handleGetStarted}
          variant="primary"
          style={styles.ctaButton}
          textStyle={styles.ctaButtonText}
        />
      </View>
      <View style={styles.subtitleContainer}>
        <Text style={styles.subtitle}>Free consultation available</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
  },
  headerContainer: {
    height: 87,
    paddingHorizontal: Spacing.base,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
    justifyContent: "center",
  },
  sectionTitle: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: 28,
    color: Colors.text,
  },
  buttonContainer: {
    paddingHorizontal: Spacing.base,
    paddingVertical: Spacing.md,
  },
  ctaButton: {
    height: 48,
    paddingHorizontal: Spacing.lg,
    flex: 1,
  },
  ctaButtonText: {
    fontSize: Typography.fontSize.base,
    lineHeight: 24,
  },
  subtitleContainer: {
    paddingTop: 4,
    paddingHorizontal: Spacing.base,
    paddingBottom: Spacing.md,
    alignItems: "center",
  },
  subtitle: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.normal,
    lineHeight: 21,
    color: Colors.textSecondary,
    textAlign: "center",
  },
});

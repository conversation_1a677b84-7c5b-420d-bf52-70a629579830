import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { Colors } from "../constants/Colors";
import { Spacing } from "../constants/Spacing";
import { Typography } from "../constants/Typography";
import { BenefitCard } from "./BenefitCard";
import {
    BrainIcon,
    ClockIcon,
    GraduationIcon,
    HeartIcon,
    ShieldIcon,
    UserIcon,
} from "./Icons";

export const BenefitsSection: React.FC = () => {
  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.sectionTitle}>Why Choose AZMed+?</Text>
      </View>
      <View style={styles.benefitsContainer}>
        <View style={styles.row}>
          <BenefitCard
            icon={<ClockIcon width={24} height={24} color={Colors.text} />}
            title="24/7 Expert Access"
            style={styles.card}
          />
          <BenefitCard
            icon={<ShieldIcon width={24} height={24} color={Colors.text} />}
            title="Secure & Private"
            style={styles.card}
          />
        </View>
        <View style={styles.row}>
          <BenefitCard
            icon={<BrainIcon width={24} height={24} color={Colors.text} />}
            title="AI-Powered Insights"
            style={[styles.card, styles.tallCard]}
          />
          <View style={styles.rightColumn}>
            <BenefitCard
              icon={<HeartIcon width={24} height={24} color={Colors.text} />}
              title="Comprehensive Tracking"
              style={styles.smallCard}
            />
          </View>
        </View>
        <View style={styles.row}>
          <BenefitCard
            icon={<GraduationIcon width={24} height={24} color={Colors.text} />}
            title="Professional Education"
            style={[styles.card, styles.tallCard]}
          />
          <BenefitCard
            icon={<UserIcon width={24} height={24} color={Colors.text} />}
            title="Multi-language Support"
            style={styles.card}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 0,
    backgroundColor: Colors.white,
  },
  headerContainer: {
    height: 60,
    paddingHorizontal: Spacing.base,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
    justifyContent: "center",
  },
  sectionTitle: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: 28,
    color: Colors.text,
  },
  benefitsContainer: {
    paddingHorizontal: Spacing.base,
    gap: Spacing.md,
  },
  row: {
    flexDirection: "row",
    gap: Spacing.md,
  },
  card: {
    flex: 1,
  },
  tallCard: {
    height: 74,
  },
  rightColumn: {
    flex: 1,
  },
  smallCard: {
    height: 74,
  },
});

import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { Colors } from "../constants/Colors";
import { Spacing } from "../constants/Spacing";
import { Typography } from "../constants/Typography";
import { MenuIcon } from "./Icons";

export const Header: React.FC = () => {
  return (
    <View style={styles.header}>
      <View style={styles.logoContainer}>
        <Text style={styles.logo}>AZMed+</Text>
      </View>
      <TouchableOpacity style={styles.menuButton} activeOpacity={0.7}>
        <MenuIcon width={24} height={24} color={Colors.text} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: Spacing.base,
    paddingTop: Spacing.base,
    paddingBottom: Spacing.sm,
    backgroundColor: Colors.white,
  },
  logoContainer: {
    flex: 1,
    alignItems: "center",
    paddingLeft: 48,
  },
  logo: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: 23,
    color: Colors.text,
    textAlign: "center",
  },
  menuButton: {
    width: 48,
    height: 48,
    justifyContent: "center",
    alignItems: "center",
  },
});

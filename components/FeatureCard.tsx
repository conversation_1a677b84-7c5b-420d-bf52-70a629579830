import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { Colors } from "../constants/Colors";
import { Spacing } from "../constants/Spacing";
import { Typography } from "../constants/Typography";

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  style?: any;
}

export const FeatureCard: React.FC<FeatureCardProps> = ({
  icon,
  title,
  description,
  style,
}) => {
  return (
    <View style={[styles.card, style]}>
      <View style={styles.iconContainer}>{icon}</View>
      <View style={styles.content}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{title}</Text>
        </View>
        <Text style={styles.description}>{description}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    flex: 1,
    padding: Spacing.base,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.cardBackground,
    gap: Spacing.md,
  },
  iconContainer: {
    width: 24,
    height: 24,
  },
  content: {
    flex: 1,
    gap: Spacing.xs,
  },
  titleContainer: {
    minHeight: 40,
    justifyContent: "center",
  },
  title: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: 20,
    color: Colors.text,
  },
  description: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.normal,
    lineHeight: 21,
    color: Colors.textSecondary,
  },
});

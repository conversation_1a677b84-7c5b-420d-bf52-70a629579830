import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { Colors } from "../constants/Colors";
import { Spacing } from "../constants/Spacing";
import { Typography } from "../constants/Typography";
import { FeatureCard } from "./FeatureCard";
import { StudentIcon, UserIcon, UsersIcon } from "./Icons";

export const WhoWeServeSection: React.FC = () => {
  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.sectionTitle}>Who We Serve</Text>
      </View>
      <View style={styles.cardsContainer}>
        <View style={styles.row}>
          <FeatureCard
            icon={<UserIcon width={24} height={24} color={Colors.text} />}
            title="Patients & Women"
            description="Personal health management"
            style={styles.card}
          />
          <FeatureCard
            icon={<UsersIcon width={24} height={24} color={Colors.text} />}
            title="Healthcare Professionals"
            description="Advanced tools & training"
            style={styles.card}
          />
        </View>
        <View style={styles.bottomRow}>
          <FeatureCard
            icon={<StudentIcon width={24} height={24} color={Colors.text} />}
            title="Medical Students"
            description="Educational resources & simulations"
            style={styles.studentCard}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 0,
    backgroundColor: Colors.white,
  },
  headerContainer: {
    height: 60,
    paddingHorizontal: Spacing.base,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
    justifyContent: "center",
  },
  sectionTitle: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: 28,
    color: Colors.text,
  },
  cardsContainer: {
    paddingHorizontal: Spacing.base,
    gap: Spacing.md,
  },
  row: {
    flexDirection: "row",
    gap: Spacing.md,
  },
  bottomRow: {
    flexDirection: "row",
    justifyContent: "flex-start",
  },
  card: {
    flex: 1,
  },
  studentCard: {
    width: 173,
  },
});

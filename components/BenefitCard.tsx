import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { Colors } from "../constants/Colors";
import { Spacing } from "../constants/Spacing";
import { Typography } from "../constants/Typography";

interface BenefitCardProps {
  icon: React.ReactNode;
  title: string;
  style?: any;
}

export const BenefitCard: React.FC<BenefitCardProps> = ({
  icon,
  title,
  style,
}) => {
  return (
    <View style={[styles.card, style]}>
      <View style={styles.iconContainer}>{icon}</View>
      <View style={styles.titleContainer}>
        <Text style={styles.title}>{title}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    flex: 1,
    padding: Spacing.base,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.cardBackground,
    flexDirection: "row",
    alignItems: "center",
    gap: Spacing.md,
  },
  iconContainer: {
    width: 24,
    height: 24,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: 20,
    color: Colors.text,
  },
});

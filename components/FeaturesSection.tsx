import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { Colors } from "../constants/Colors";
import { Spacing } from "../constants/Spacing";
import { Typography } from "../constants/Typography";
import { FeatureCard } from "./FeatureCard";
import { BookIcon, ChartIcon, PhoneIcon, SearchIcon } from "./Icons";

export const FeaturesSection: React.FC = () => {
  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.sectionTitle}>
          Comprehensive Care for Every Stage
        </Text>
      </View>
      <View style={styles.featuresContainer}>
        <View style={styles.row}>
          <FeatureCard
            icon={<PhoneIcon width={24} height={24} color={Colors.text} />}
            title="Telemedicine Consultations"
            description="Video calls with specialists"
            style={styles.card}
          />
          <FeatureCard
            icon={<ChartIcon width={24} height={24} color={Colors.text} />}
            title="AI Health Tracking"
            description="Cycle tracking & pregnancy monitoring"
            style={styles.card}
          />
        </View>
        <View style={styles.row}>
          <FeatureCard
            icon={<BookIcon width={24} height={24} color={Colors.text} />}
            title="Educational Courses"
            description="Professional training & patient education"
            style={styles.card}
          />
          <FeatureCard
            icon={<SearchIcon width={24} height={24} color={Colors.text} />}
            title="Risk Analysis"
            description="Early detection & personalized insights"
            style={[styles.card, styles.riskAnalysisCard]}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 0,
    backgroundColor: Colors.white,
  },
  headerContainer: {
    height: 87,
    paddingHorizontal: Spacing.base,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
    justifyContent: "center",
  },
  sectionTitle: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: 28,
    color: Colors.text,
  },
  featuresContainer: {
    paddingHorizontal: Spacing.base,
    gap: Spacing.md,
  },
  row: {
    flexDirection: "row",
    gap: Spacing.md,
  },
  card: {
    flex: 1,
    minHeight: 177,
  },
  riskAnalysisCard: {
    minHeight: 156,
  },
});

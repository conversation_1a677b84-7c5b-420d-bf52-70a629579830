import React from "react";
import { StyleSheet, Text, View } from "react-native";
import { Colors } from "../constants/Colors";
import { Spacing } from "../constants/Spacing";
import { Typography } from "../constants/Typography";

export const TestimonialsSection: React.FC = () => {
  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.sectionTitle}>
          Trusted by Healthcare Professionals
        </Text>
      </View>
      <View style={styles.logoContainer}>
        {/* Placeholder for partner logos */}
        <View style={styles.logoRow}>
          <View style={[styles.logo, styles.largeLogo]} />
          <View style={[styles.logo, styles.smallLogo]} />
          <View style={[styles.logo, styles.smallLogo]} />
        </View>
      </View>
      <View style={styles.testimonialContainer}>
        <Text style={styles.testimonial}>
          "AZMed+ has revolutionized my practice. The AI-driven insights are
          invaluable for patient care." - Dr<PERSON> <PERSON>, OB/GYN
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
  },
  headerContainer: {
    height: 87,
    paddingHorizontal: Spacing.base,
    paddingTop: Spacing.lg,
    paddingBottom: Spacing.md,
    justifyContent: "center",
  },
  sectionTitle: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    lineHeight: 28,
    color: Colors.text,
  },
  logoContainer: {
    paddingHorizontal: Spacing.base,
    backgroundColor: Colors.white,
  },
  logoRow: {
    flexDirection: "row",
    gap: 4,
    flex: 1,
  },
  logo: {
    backgroundColor: Colors.border,
    borderRadius: 8,
    height: 60,
  },
  largeLogo: {
    width: 175,
  },
  smallLogo: {
    width: 88,
  },
  testimonialContainer: {
    paddingTop: 4,
    paddingHorizontal: Spacing.base,
    paddingBottom: Spacing.md,
    alignItems: "center",
  },
  testimonial: {
    fontFamily: Typography.fontFamily,
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.normal,
    lineHeight: 24,
    color: Colors.text,
    textAlign: "center",
  },
});

import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ie<PERSON>,
  <PERSON>Bar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";

const Colors = {
  primary: "#1A94E5",
  text: "#121417",
  textSecondary: "#637887",
  white: "#FFF",
  background: "#FFF",
  inputBackground: "#F0F2F5",
  disabledText: "#DCE5EF",
  gradientStart: "rgba(0, 0, 0, 0.40)",
  gradientEnd: "rgba(0, 0, 0, 0.00)",
};

const InputField = ({
  placeholder,
  value,
  onChangeText,
  secureTextEntry = false,
}) => (
  <View style={styles.inputContainer}>
    <View style={styles.inputWrapper}>
      <TextInput
        style={styles.textInput}
        placeholder={placeholder}
        placeholderTextColor={Colors.textSecondary}
        value={value}
        onChangeText={onChangeText}
        secureTextEntry={secureTextEntry}
      />
    </View>
  </View>
);

const SocialButton = ({ title, onPress, disabled = false }) => (
  <TouchableOpacity
    style={[styles.socialButton, disabled && styles.socialButtonDisabled]}
    onPress={onPress}
    activeOpacity={disabled ? 1 : 0.7}
    disabled={disabled}
  >
    <Text
      style={[
        styles.socialButtonText,
        disabled && styles.socialButtonTextDisabled,
      ]}
    >
      {title}
    </Text>
  </TouchableOpacity>
);

export default function LoginScreen() {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert(
        "Eksik Bilgi",
        "Lütfen e-posta ve şifre alanlarını doldurun.",
      );
      return;
    }

    setIsLoading(true);
    console.log("Login attempted with:", { email, password }); // Debug log

    // Simulate a small delay for better UX
    setTimeout(() => {
      // Trim whitespace and check credentials
      const trimmedEmail = email.trim().toLowerCase();
      const trimmedPassword = password.trim();

      if (trimmedEmail === "demo" && trimmedPassword === "demo") {
        // Navigate directly to dashboard (now the root page)
        setIsLoading(false);
        router.push("/");

        // Show success message after navigation
        setTimeout(() => {
          Alert.alert("Başarılı!", "Hoş geldiniz! Giriş başarılı.");
        }, 500);
      } else {
        setIsLoading(false);
        Alert.alert(
          "Giriş Hatası",
          `Kullanıcı adı veya şifre hatalı.\n\nDoğru bilgiler:\nKullanıcı adı: demo\nŞifre: demo\n\nGirdiğiniz: ${trimmedEmail}/${trimmedPassword}`,
          [{ text: "Tamam" }],
        );
      }
    }, 300);
  };

  const handleForgotPassword = () => {
    Alert.alert(
      "Şifremi Unuttum",
      "Şifre sıfırlama bağlantısı e-posta adresinize gönderilecek.",
    );
  };

  const handleSocialLogin = (provider: string) => {
    Alert.alert(
      `${provider} ile Giriş`,
      `${provider} ile giriş özelliği yakında aktif olacak.`,
    );
  };

  const handleSignUp = () => {
    router.push("/signup");
  };

  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.welcomeTitle}>Hoş geldiniz</Text>
        </View>

        {/* Hero Section with Branding */}
        <View style={styles.heroSection}>
          <LinearGradient
            colors={[Colors.gradientEnd, Colors.gradientStart]}
            style={styles.heroGradient}
          >
            <View style={styles.heroContent}>
              <View style={styles.brandingContainer}>
                <Text style={styles.brandTitle}>AZMed+</Text>
                <Text style={styles.brandSubtitle}>
                  Sağlık hizmetlerinizi yönetin
                </Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Login Form */}
        <View style={styles.formSection}>
          <InputField
            placeholder="E-posta"
            value={email}
            onChangeText={setEmail}
          />

          <InputField
            placeholder="Parola"
            value={password}
            onChangeText={setPassword}
            secureTextEntry={true}
          />

          <TouchableOpacity
            style={styles.forgotPasswordContainer}
            onPress={handleForgotPassword}
          >
            <Text style={styles.forgotPasswordText}>Şifremi unuttum</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.loginButton,
              isLoading && styles.loginButtonDisabled,
            ]}
            onPress={handleLogin}
            activeOpacity={0.8}
            disabled={isLoading}
          >
            <Text style={styles.loginButtonText}>
              {isLoading ? "Giriş yapılıyor..." : "Giriş yap"}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Social Login Section */}
        <View style={styles.socialSection}>
          <Text style={styles.socialTitle}>Veya ıle devam et</Text>

          <View style={styles.socialButtonsRow}>
            <SocialButton
              title="Google"
              onPress={() => handleSocialLogin("Google")}
            />
            <SocialButton
              title="Apple"
              onPress={() => handleSocialLogin("Apple")}
              disabled={true}
            />
          </View>

          <View style={styles.socialButtonsRow}>
            <SocialButton
              title="Facebook"
              onPress={() => handleSocialLogin("Facebook")}
              disabled={true}
            />
            <SocialButton
              title="Hasta"
              onPress={() => handleSocialLogin("Hasta")}
              disabled={true}
            />
          </View>

          <View style={styles.socialButtonsRow}>
            <SocialButton
              title="Doktor"
              onPress={() => handleSocialLogin("Doktor")}
              disabled={true}
            />
            <SocialButton title="Kayıt ol" onPress={handleSignUp} />
          </View>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  contentContainer: {
    flexGrow: 1,
  },

  // Header Styles
  header: {
    padding: 20,
    paddingBottom: 12,
    alignItems: "center",
  },
  welcomeTitle: {
    fontFamily: "Lexend",
    fontSize: 28,
    fontWeight: "700",
    lineHeight: 35,
    color: Colors.text,
    textAlign: "center",
  },

  // Hero Section Styles
  heroSection: {
    height: 551,
    marginHorizontal: 16,
    borderRadius: 8,
    overflow: "hidden",
    backgroundColor: "#5A8A9C", // Fallback color
  },
  heroGradient: {
    flex: 1,
    justifyContent: "flex-end",
  },
  heroContent: {
    padding: 16,
    paddingTop: 535,
  },
  brandingContainer: {
    maxWidth: 440,
    gap: 4,
  },
  brandTitle: {
    fontFamily: "Lexend",
    fontSize: 24,
    fontWeight: "700",
    lineHeight: 30,
    color: Colors.white,
  },
  brandSubtitle: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.white,
  },

  // Form Section Styles
  formSection: {
    paddingHorizontal: 16,
    paddingTop: 12,
  },
  inputContainer: {
    paddingVertical: 12,
  },
  inputWrapper: {
    height: 56,
    borderRadius: 8,
    backgroundColor: Colors.inputBackground,
    justifyContent: "center",
  },
  textInput: {
    flex: 1,
    paddingHorizontal: 16,
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.text,
  },

  forgotPasswordContainer: {
    paddingHorizontal: 4,
    paddingTop: 4,
    paddingBottom: 12,
  },
  forgotPasswordText: {
    fontFamily: "Lexend",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.textSecondary,
  },

  loginButton: {
    height: 48,
    backgroundColor: Colors.primary,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 12,
  },
  loginButtonDisabled: {
    backgroundColor: Colors.textSecondary,
  },
  loginButtonText: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    color: Colors.white,
    textAlign: "center",
  },

  // Social Section Styles
  socialSection: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  socialTitle: {
    fontFamily: "Lexend",
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 23,
    color: Colors.text,
    marginBottom: 8,
  },

  socialButtonsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 12,
    paddingVertical: 12,
  },
  socialButton: {
    flex: 1,
    height: 40,
    backgroundColor: Colors.inputBackground,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 16,
  },
  socialButtonDisabled: {
    backgroundColor: Colors.inputBackground,
  },
  socialButtonText: {
    fontFamily: "Lexend",
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 21,
    color: Colors.text,
    textAlign: "center",
  },
  socialButtonTextDisabled: {
    color: Colors.disabledText,
  },
});

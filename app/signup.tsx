import { useRouter } from "expo-router";
import React, { useState } from "react";
import {
    ScrollView,
    StatusBar,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from "react-native";

const Colors = {
  primary: "#FF5C8A",
  text: "#170F12",
  textSecondary: "#8C5E6B",
  white: "#FFF",
  background: "#F5F0F2",
  border: "#E8D9DE",
};

const BackIcon = () => <Text style={styles.backIcon}>←</Text>;

const EyeIcon = () => <Text style={styles.eyeIcon}>👁</Text>;

const CalendarIcon = () => <Text style={styles.calendarIcon}>📅</Text>;

const ArrowIcon = () => (
  <View style={styles.arrowContainer}>
    <Text style={styles.arrowUp}>▲</Text>
    <Text style={styles.arrowDown}>▼</Text>
  </View>
);

const InputField = ({
  label,
  placeholder,
  value,
  onChangeText,
  secureTextEntry,
  rightIcon,
  multiline = false,
  style,
}) => (
  <View style={[styles.inputContainer, style]}>
    <Text style={styles.inputLabel}>{label}</Text>
    <View style={styles.inputWrapper}>
      <TextInput
        style={[styles.textInput, multiline && styles.multilineInput]}
        placeholder={placeholder}
        placeholderTextColor={Colors.textSecondary}
        value={value}
        onChangeText={onChangeText}
        secureTextEntry={secureTextEntry}
        multiline={multiline}
      />
      {rightIcon && <View style={styles.inputIcon}>{rightIcon}</View>}
    </View>
  </View>
);

const NumberInput = ({ label, value, onChangeText }) => (
  <View style={styles.inputContainer}>
    <Text style={styles.inputLabel}>{label}</Text>
    <View style={styles.numberInputWrapper}>
      <Text style={styles.numberValue}>{value}</Text>
      <ArrowIcon />
    </View>
  </View>
);

const CheckboxItem = ({ label, checked, onPress }) => (
  <TouchableOpacity
    style={styles.checkboxContainer}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <View style={[styles.checkbox, checked && styles.checkboxChecked]}>
      {checked && <Text style={styles.checkboxMark}>✓</Text>}
    </View>
    <Text style={styles.checkboxLabel}>{label}</Text>
  </TouchableOpacity>
);

export default function PatientSignup() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    password: "",
    confirmPassword: "",
    birthDate: "",
    phoneNumber: "",
    pregnancies: "1",
    births: "1",
    livingChildren: "1",
    knownDiseases: "Yok",
    medications: "",
    operations: "",
    smokingUse: false,
    alcoholUse: false,
    termsAccepted: false,
    privacyAccepted: false,
  });

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleCheckboxChange = (field) => {
    setFormData((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  const handleRegister = () => {
    console.log("Registration data:", formData);
    // Handle registration logic here
  };

  const handleGoBack = () => {
    router.back();
  };

  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleGoBack}
            activeOpacity={0.7}
          >
            <BackIcon />
          </TouchableOpacity>
          <View style={styles.titleContainer}>
            <Text style={styles.headerTitle}>Hasta Olarak</Text>
          </View>
        </View>

        {/* Progress Indicators */}
        <View style={styles.progressContainer}>
          <View style={[styles.progressDot, styles.progressDotInactive]} />
          <View style={[styles.progressDot, styles.progressDotActive]} />
          <View style={[styles.progressDot, styles.progressDotInactive]} />
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Personal Information */}
          <InputField
            label="Ad Soyad"
            placeholder="Ad Soyad"
            value={formData.fullName}
            onChangeText={(value) => handleInputChange("fullName", value)}
          />

          <InputField
            label="E-posta"
            placeholder="E-posta"
            value={formData.email}
            onChangeText={(value) => handleInputChange("email", value)}
          />

          <InputField
            label="Şifre"
            placeholder="Şifre"
            value={formData.password}
            onChangeText={(value) => handleInputChange("password", value)}
            secureTextEntry={true}
            rightIcon={<EyeIcon />}
          />

          <InputField
            label="Şifre Tekrar"
            placeholder="Şifre Tekrar"
            value={formData.confirmPassword}
            onChangeText={(value) =>
              handleInputChange("confirmPassword", value)
            }
            secureTextEntry={true}
            rightIcon={<EyeIcon />}
          />

          <InputField
            label="Doğum Tarihi"
            placeholder="Doğum Tarihi"
            value={formData.birthDate}
            onChangeText={(value) => handleInputChange("birthDate", value)}
            rightIcon={<CalendarIcon />}
          />

          <InputField
            label="Telefon Numarası"
            placeholder="Telefon Numarası"
            value={formData.phoneNumber}
            onChangeText={(value) => handleInputChange("phoneNumber", value)}
          />

          {/* Medical History */}
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Medical History</Text>
          </View>

          <NumberInput
            label="Gebelik Sayısı"
            value={formData.pregnancies}
            onChangeText={(value) => handleInputChange("pregnancies", value)}
          />

          <NumberInput
            label="Doğum Sayısı"
            value={formData.births}
            onChangeText={(value) => handleInputChange("births", value)}
          />

          <NumberInput
            label="Yaşayan Çocuk Sayısı"
            value={formData.livingChildren}
            onChangeText={(value) => handleInputChange("livingChildren", value)}
          />

          <NumberInput
            label="Bilinen Hastalıklar"
            value={formData.knownDiseases}
            onChangeText={(value) => handleInputChange("knownDiseases", value)}
          />

          <InputField
            label="Kullanılan İlaçlar"
            placeholder=""
            value={formData.medications}
            onChangeText={(value) => handleInputChange("medications", value)}
            multiline={true}
            style={styles.textAreaContainer}
          />

          <InputField
            label="Geçirilen Operasyonlar"
            placeholder=""
            value={formData.operations}
            onChangeText={(value) => handleInputChange("operations", value)}
            multiline={true}
            style={styles.textAreaContainer}
          />

          {/* Checkboxes */}
          <View style={styles.checkboxSection}>
            <CheckboxItem
              label="Sigara Kullanımı"
              checked={formData.smokingUse}
              onPress={() => handleCheckboxChange("smokingUse")}
            />
            <CheckboxItem
              label="Alkol Kullanımı"
              checked={formData.alcoholUse}
              onPress={() => handleCheckboxChange("alcoholUse")}
            />
          </View>

          <View style={styles.checkboxSection}>
            <CheckboxItem
              label="I agree to the terms and conditions"
              checked={formData.termsAccepted}
              onPress={() => handleCheckboxChange("termsAccepted")}
            />
            <CheckboxItem
              label="I agree to the privacy policy"
              checked={formData.privacyAccepted}
              onPress={() => handleCheckboxChange("privacyAccepted")}
            />
          </View>

          {/* Register Button */}
          <TouchableOpacity
            style={styles.registerButton}
            onPress={handleRegister}
            activeOpacity={0.8}
          >
            <Text style={styles.registerButtonText}>Kayıt Ol</Text>
          </TouchableOpacity>

          <View style={styles.bottomSpacer} />
        </ScrollView>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },

  // Header Styles
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: Colors.white,
  },
  backButton: {
    width: 48,
    height: 48,
    justifyContent: "center",
    alignItems: "center",
  },
  backIcon: {
    fontSize: 20,
    color: Colors.text,
  },
  titleContainer: {
    flex: 1,
    alignItems: "center",
    paddingRight: 48,
  },
  headerTitle: {
    fontFamily: "Lexend",
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 23,
    color: Colors.text,
    textAlign: "center",
  },

  // Progress Styles
  progressContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: 12,
    paddingVertical: 20,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  progressDotActive: {
    backgroundColor: Colors.text,
  },
  progressDotInactive: {
    backgroundColor: Colors.border,
  },

  // Scroll Styles
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },

  // Input Styles
  inputContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  inputLabel: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.text,
    paddingBottom: 8,
  },
  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    height: 56,
    borderRadius: 12,
    backgroundColor: Colors.background,
  },
  textInput: {
    flex: 1,
    paddingHorizontal: 16,
    fontFamily: "Lexend",
    fontSize: 15,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.text,
  },
  multilineInput: {
    height: 144,
    paddingTop: 16,
    textAlignVertical: "top",
  },
  inputIcon: {
    paddingRight: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  eyeIcon: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  calendarIcon: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  textAreaContainer: {
    // No additional styles needed
  },

  // Number Input Styles
  numberInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    height: 56,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: Colors.background,
  },
  numberValue: {
    fontFamily: "Lexend",
    fontSize: 15,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.text,
  },
  arrowContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  arrowUp: {
    fontSize: 12,
    color: Colors.textSecondary,
    lineHeight: 14,
  },
  arrowDown: {
    fontSize: 12,
    color: Colors.textSecondary,
    lineHeight: 14,
  },

  // Section Styles
  sectionHeader: {
    height: 60,
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 12,
    justifyContent: "center",
  },
  sectionTitle: {
    fontFamily: "Lexend",
    fontSize: 22,
    fontWeight: "700",
    lineHeight: 28,
    color: Colors.text,
  },

  // Checkbox Styles
  checkboxSection: {
    paddingHorizontal: 16,
  },
  checkboxContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    gap: 12,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: Colors.border,
    justifyContent: "center",
    alignItems: "center",
  },
  checkboxChecked: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  checkboxMark: {
    color: Colors.white,
    fontSize: 12,
    fontWeight: "bold",
  },
  checkboxLabel: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.text,
    flex: 1,
  },

  // Button Styles
  registerButton: {
    height: 48,
    marginHorizontal: 16,
    marginVertical: 12,
    paddingHorizontal: 20,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 12,
    backgroundColor: Colors.primary,
  },
  registerButtonText: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    color: Colors.white,
    textAlign: "center",
  },

  // Spacer
  bottomSpacer: {
    height: 20,
    backgroundColor: Colors.white,
  },
});

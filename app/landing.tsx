import { useRouter } from "expo-router";
import React from "react";
import {
  <PERSON><PERSON>View,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

const Colors = {
  primary: "#2496F2",
  text: "#121417",
  textSecondary: "#61788A",
  white: "#FFF",
  border: "#DBE0E5",
  grayBackground: "#F0F2F5",
};

const Button = ({ title, onPress, variant = "primary", style }) => (
  <TouchableOpacity
    style={[
      styles.button,
      variant === "primary" ? styles.primaryButton : styles.secondaryButton,
      style,
    ]}
    onPress={onPress}
    activeOpacity={0.8}
  >
    <Text
      style={[
        styles.buttonText,
        variant === "primary" ? styles.primaryText : styles.secondaryText,
      ]}
    >
      {title}
    </Text>
  </TouchableOpacity>
);

const FeatureCard = ({ title, description, style }) => (
  <View style={[styles.card, style]}>
    <View style={styles.iconPlaceholder} />
    <View style={styles.cardContent}>
      <Text style={styles.cardTitle}>{title}</Text>
      <Text style={styles.cardDescription}>{description}</Text>
    </View>
  </View>
);

const Header = ({ onLogin }) => (
  <View style={styles.header}>
    <View style={styles.logoContainer}>
      <Text style={styles.logo}>AZMed+</Text>
    </View>
    <View style={styles.headerActions}>
      <TouchableOpacity
        style={styles.loginButton}
        onPress={onLogin}
        activeOpacity={0.7}
      >
        <Text style={styles.loginButtonText}>Giriş</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.menuButton} activeOpacity={0.7}>
        <Text style={styles.menuIcon}>☰</Text>
      </TouchableOpacity>
    </View>
  </View>
);

const HeroSection = ({ onStartJourney, onBookConsultation, onLogin }) => (
  <View style={styles.heroContainer}>
    <View style={styles.heroContent}>
      <View style={styles.heroTextContainer}>
        <Text style={styles.heroTitle}>
          Your Complete Women's Health Digital Companion
        </Text>
        <Text style={styles.heroSubtitle}>
          From pregnancy planning to menopause - AI-powered care, expert
          consultations, and personalized health tracking
        </Text>
      </View>
      <View style={styles.heroButtonContainer}>
        <Button
          title="Start Your Health Journey"
          onPress={onStartJourney}
          variant="primary"
          style={styles.heroButton}
        />
        <Button
          title="Book Consultation"
          onPress={onBookConsultation}
          variant="secondary"
          style={styles.heroButton}
        />
      </View>
    </View>
  </View>
);

export default function LandingPage() {
  const router = useRouter();

  const handleGetStarted = () => {
    router.push("/signup");
  };

  const handleStartJourney = () => {
    router.push("/signup");
  };

  const handleLogin = () => {
    router.push("/login");
  };

  const handleBookConsultation = () => {
    console.log("Book Consultation");
  };

  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <Header onLogin={handleLogin} />
        <HeroSection
          onStartJourney={handleStartJourney}
          onBookConsultation={handleBookConsultation}
          onLogin={handleLogin}
        />

        {/* Features Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Comprehensive Care for Every Stage
          </Text>
          <View style={styles.featuresGrid}>
            <View style={styles.featuresRow}>
              <FeatureCard
                title="Telemedicine Consultations"
                description="Video calls with specialists"
                style={styles.featureCard}
              />
              <FeatureCard
                title="AI Health Tracking"
                description="Cycle tracking & pregnancy monitoring"
                style={styles.featureCard}
              />
            </View>
            <View style={styles.featuresRow}>
              <FeatureCard
                title="Educational Courses"
                description="Professional training & patient education"
                style={styles.featureCard}
              />
              <FeatureCard
                title="Risk Analysis"
                description="Early detection & personalized insights"
                style={styles.featureCard}
              />
            </View>
          </View>
        </View>

        {/* Benefits Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Why Choose AZMed+?</Text>
          <View style={styles.benefitsGrid}>
            <View style={styles.benefitRow}>
              <View style={styles.benefitCard}>
                <View style={styles.iconPlaceholder} />
                <Text style={styles.benefitTitle}>24/7 Expert Access</Text>
              </View>
              <View style={styles.benefitCard}>
                <View style={styles.iconPlaceholder} />
                <Text style={styles.benefitTitle}>Secure & Private</Text>
              </View>
            </View>
            <View style={styles.benefitRow}>
              <View style={styles.benefitCard}>
                <View style={styles.iconPlaceholder} />
                <Text style={styles.benefitTitle}>AI-Powered Insights</Text>
              </View>
              <View style={styles.benefitCard}>
                <View style={styles.iconPlaceholder} />
                <Text style={styles.benefitTitle}>Comprehensive Tracking</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Who We Serve Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Who We Serve</Text>
          <View style={styles.featuresGrid}>
            <View style={styles.featuresRow}>
              <FeatureCard
                title="Patients & Women"
                description="Personal health management"
                style={styles.featureCard}
              />
              <FeatureCard
                title="Healthcare Professionals"
                description="Advanced tools & training"
                style={styles.featureCard}
              />
            </View>
            <View style={styles.featuresRow}>
              <FeatureCard
                title="Medical Students"
                description="Educational resources & simulations"
                style={[styles.featureCard, { flex: 0.7 }]}
              />
            </View>
          </View>
        </View>

        {/* Testimonials Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Trusted by Healthcare Professionals
          </Text>
          <View style={styles.logoContainer}>
            <View style={styles.logoPlaceholder} />
            <View style={styles.logoPlaceholder} />
            <View style={styles.logoPlaceholder} />
          </View>
          <Text style={styles.testimonial}>
            "AZMed+ has revolutionized my practice. The AI-driven insights are
            invaluable for patient care." - Dr. Anya Sharma, OB/GYN
          </Text>
        </View>

        {/* CTA Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Ready to Transform Your Health Journey?
          </Text>
          <View style={styles.ctaButtonContainer}>
            <Button
              title="Get Started Today"
              onPress={handleGetStarted}
              variant="primary"
              style={styles.ctaButton}
            />
            <Button
              title="Already have an account? Login"
              onPress={handleLogin}
              variant="secondary"
              style={styles.ctaButton}
            />
          </View>
          <Text style={styles.ctaSubtitle}>Free consultation available</Text>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <View style={styles.footerLinks}>
            <Text style={styles.footerLink}>Services</Text>
            <Text style={styles.footerLink}>About</Text>
            <Text style={styles.footerLink}>Contact</Text>
            <Text style={styles.footerLink}>Privacy Policy</Text>
          </View>
          <Text style={styles.copyright}>
            © 2024 AZMed+. All rights reserved.
          </Text>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  contentContainer: {
    flexGrow: 1,
  },

  // Header Styles
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: Colors.white,
  },
  logoContainer: {
    flex: 1,
    alignItems: "center",
  },
  logo: {
    fontSize: 18,
    fontWeight: "700",
    color: Colors.text,
    textAlign: "center",
  },
  headerActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  loginButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: Colors.primary,
    borderRadius: 6,
  },
  loginButtonText: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.white,
  },
  menuButton: {
    width: 48,
    height: 48,
    justifyContent: "center",
    alignItems: "center",
  },
  menuIcon: {
    fontSize: 20,
    color: Colors.text,
  },

  // Hero Styles
  heroContainer: {
    minHeight: 480,
    backgroundColor: "#5A8A9C",
    justifyContent: "flex-end",
    paddingHorizontal: 16,
    paddingTop: 151,
    paddingBottom: 32,
  },
  heroContent: {
    gap: 24,
  },
  heroTextContainer: {
    gap: 8,
    alignItems: "center",
  },
  heroTitle: {
    fontSize: 36,
    fontWeight: "700",
    lineHeight: 45,
    color: Colors.white,
    textAlign: "center",
    letterSpacing: -1,
  },
  heroSubtitle: {
    fontSize: 14,
    lineHeight: 21,
    color: Colors.white,
    textAlign: "center",
  },
  heroButtonContainer: {
    flexDirection: "row",
    justifyContent: "center",
    gap: 12,
    flexWrap: "wrap",
  },
  heroButton: {
    minWidth: 120,
  },

  // Button Styles
  button: {
    height: 40,
    paddingHorizontal: 16,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    minWidth: 84,
  },
  primaryButton: {
    backgroundColor: Colors.primary,
  },
  secondaryButton: {
    backgroundColor: Colors.grayBackground,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 21,
    textAlign: "center",
  },
  primaryText: {
    color: Colors.white,
  },
  secondaryText: {
    color: Colors.text,
  },

  // Section Styles
  section: {
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: "700",
    lineHeight: 28,
    color: Colors.text,
    marginBottom: 16,
  },

  // Feature Card Styles
  featuresGrid: {
    gap: 12,
  },
  featuresRow: {
    flexDirection: "row",
    gap: 12,
  },
  featureCard: {
    flex: 1,
  },
  card: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.white,
    gap: 12,
  },
  iconPlaceholder: {
    width: 24,
    height: 24,
    backgroundColor: Colors.border,
    borderRadius: 4,
  },
  cardContent: {
    gap: 4,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 20,
    color: Colors.text,
  },
  cardDescription: {
    fontSize: 14,
    lineHeight: 21,
    color: Colors.textSecondary,
  },

  // Benefit Card Styles
  benefitsGrid: {
    gap: 12,
  },
  benefitRow: {
    flexDirection: "row",
    gap: 12,
  },
  benefitCard: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.white,
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 20,
    color: Colors.text,
    flex: 1,
  },

  // Logo and Testimonial Styles
  logoPlaceholder: {
    height: 40,
    backgroundColor: Colors.border,
    borderRadius: 4,
    flex: 1,
    marginHorizontal: 2,
  },
  testimonial: {
    fontSize: 16,
    lineHeight: 24,
    color: Colors.text,
    textAlign: "center",
    marginTop: 16,
    fontStyle: "italic",
  },

  // CTA Styles
  ctaButtonContainer: {
    gap: 8,
  },
  ctaButton: {
    marginVertical: 4,
  },
  ctaSubtitle: {
    fontSize: 14,
    lineHeight: 21,
    color: Colors.textSecondary,
    textAlign: "center",
  },

  // Footer Styles
  footer: {
    padding: 20,
    backgroundColor: Colors.white,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  footerLinks: {
    flexDirection: "row",
    justifyContent: "space-around",
    flexWrap: "wrap",
    marginBottom: 16,
  },
  footerLink: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: "center",
    paddingVertical: 4,
  },
  copyright: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: "center",
  },
});

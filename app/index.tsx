import { useRouter } from "expo-router";
import React from "react";
import {
  Image,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import Svg, { Path } from "react-native-svg";

const Colors = {
  primary: "#171217",
  secondary: "#80618A",
  accent: "#B512EB",
  text: "#171217",
  textSecondary: "#80618A",
  white: "#FFF",
  background: "#FFF",
  searchBackground: "#F2F0F5",
  cardBackground: "#FFF",
  border: "#E3DBE5",
  bottomBorder: "#F2F0F5",
  gradientStart: "rgba(0, 0, 0, 0.40)",
  gradientEnd: "rgba(0, 0, 0, 0.00)",
};

// SVG Icons Components
const SettingsIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 20 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 5.5C7.51472 5.5 5.5 7.51472 5.5 10C5.5 12.4853 7.51472 14.5 10 14.5C12.4853 14.5 14.5 12.4853 14.5 10C14.4974 7.51579 12.4842 5.50258 10 5.5ZM10 13C8.34315 13 7 11.6569 7 10C7 8.34315 8.34315 7 10 7C11.6569 7 13 8.34315 13 10C13 11.6569 11.6569 13 10 13ZM18.25 10.2025C18.2537 10.0675 18.2537 9.9325 18.25 9.7975L19.6488 8.05C19.7975 7.86393 19.849 7.61827 19.7875 7.38813C19.5582 6.52619 19.2152 5.69861 18.7675 4.92719C18.6486 4.72249 18.4401 4.58592 18.205 4.55875L15.9813 4.31125C15.8888 4.21375 15.795 4.12 15.7 4.03L15.4375 1.80063C15.4101 1.56531 15.2732 1.35677 15.0681 1.23813C14.2964 0.791263 13.4689 0.448595 12.6072 0.219063C12.3769 0.157836 12.1312 0.209687 11.9453 0.35875L10.2025 1.75C10.0675 1.75 9.9325 1.75 9.7975 1.75L8.05 0.354063C7.86393 0.205326 7.61827 0.153827 7.38813 0.215312C6.52633 0.445025 5.6988 0.788016 4.92719 1.23531C4.72249 1.35417 4.58592 1.56268 4.55875 1.79781L4.31125 4.02531C4.21375 4.11844 4.12 4.21219 4.03 4.30656L1.80063 4.5625C1.56531 4.58988 1.35677 4.72682 1.23813 4.93188C0.791263 5.70359 0.448595 6.5311 0.219063 7.39281C0.157836 7.6231 0.209687 7.86878 0.35875 8.05469L1.75 9.7975C1.75 9.9325 1.75 10.0675 1.75 10.2025L0.354063 11.95C0.205326 12.1361 0.153827 12.3817 0.215312 12.6119C0.444615 13.4738 0.787627 14.3014 1.23531 15.0728C1.35417 15.2775 1.56268 15.4141 1.79781 15.4412L4.02156 15.6887C4.11469 15.7862 4.20844 15.88 4.30281 15.97L4.5625 18.1994C4.58988 18.4347 4.72682 18.6432 4.93188 18.7619C5.70359 19.2087 6.5311 19.5514 7.39281 19.7809C7.6231 19.8422 7.86878 19.7903 8.05469 19.6413L9.7975 18.25C9.9325 18.2537 10.0675 18.2537 10.2025 18.25L11.95 19.6488C12.1361 19.7975 12.3817 19.849 12.6119 19.7875C13.4738 19.5582 14.3014 19.2152 15.0728 18.7675C15.2775 18.6486 15.4141 18.4401 15.4412 18.205L15.6887 15.9813C15.7862 15.8888 15.88 15.795 15.97 15.7L18.1994 15.4375C18.4347 15.4101 18.6432 15.2732 18.7619 15.0681C19.2087 14.2964 19.5514 13.4689 19.7809 12.6072C19.8422 12.3769 19.7903 12.1312 19.6413 11.9453L18.25 10.2025ZM16.7406 9.59313C16.7566 9.86414 16.7566 10.1359 16.7406 10.4069C16.7295 10.5924 16.7876 10.7755 16.9037 10.9206L18.2341 12.5828C18.0814 13.0679 17.886 13.5385 17.65 13.9891L15.5312 14.2291C15.3467 14.2495 15.1764 14.3377 15.0531 14.4766C14.8727 14.6795 14.6805 14.8717 14.4775 15.0522C14.3387 15.1754 14.2505 15.3458 14.23 15.5303L13.9947 17.6472C13.5442 17.8833 13.0736 18.0787 12.5884 18.2313L10.9253 16.9009C10.7922 16.7946 10.6269 16.7367 10.4566 16.7369H10.4116C10.1405 16.7528 9.86883 16.7528 9.59781 16.7369C9.41226 16.7257 9.22918 16.7838 9.08406 16.9L7.41719 18.2313C6.93206 18.0786 6.46146 17.8831 6.01094 17.6472L5.77094 15.5312C5.75046 15.3467 5.66227 15.1764 5.52344 15.0531C5.32048 14.8727 5.12827 14.6805 4.94781 14.4775C4.82456 14.3387 4.6542 14.2505 4.46969 14.23L2.35281 13.9937C2.11674 13.5433 1.92128 13.0727 1.76875 12.5875L3.09906 10.9244C3.21522 10.7793 3.27336 10.5962 3.26219 10.4106C3.24625 10.1396 3.24625 9.86789 3.26219 9.59688C3.27336 9.41133 3.21522 9.22824 3.09906 9.08313L1.76875 7.41719C1.9214 6.93206 2.11685 6.46146 2.35281 6.01094L4.46875 5.77094C4.65326 5.75046 4.82362 5.66227 4.94688 5.52344C5.12733 5.32048 5.31954 5.12827 5.5225 4.94781C5.66188 4.82448 5.75043 4.65373 5.77094 4.46875L6.00625 2.35281C6.45672 2.11674 6.92733 1.92128 7.4125 1.76875L9.07563 3.09906C9.22074 3.21522 9.40383 3.27336 9.58937 3.26219C9.86039 3.24625 10.1321 3.24625 10.4031 3.26219C10.5887 3.27336 10.7718 3.21522 10.9169 3.09906L12.5828 1.76875C13.0679 1.9214 13.5385 2.11685 13.9891 2.35281L14.2291 4.46875C14.2495 4.65326 14.3377 4.82362 14.4766 4.94688C14.6795 5.12733 14.8717 5.31954 15.0522 5.5225C15.1754 5.66133 15.3458 5.74952 15.5303 5.77L17.6472 6.00531C17.8833 6.45578 18.0787 6.9264 18.2313 7.41156L16.9009 9.07469C16.7837 9.22103 16.7255 9.406 16.7378 9.59313H16.7406Z"
      fill={Colors.primary}
    />
  </Svg>
);

const SearchIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 20 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.5306 18.4694L14.8366 13.7762C17.6629 10.383 17.3204 5.36693 14.0591 2.38935C10.7978 -0.588237 5.77134 -0.474001 2.64867 2.64867C-0.474001 5.77134 -0.588237 10.7978 2.38935 14.0591C5.36693 17.3204 10.383 17.6629 13.7762 14.8366L18.4694 19.5306C18.7624 19.8237 19.2376 19.8237 19.5306 19.5306C19.8237 19.2376 19.8237 18.7624 19.5306 18.4694ZM1.75 8.5C1.75 4.77208 4.77208 1.75 8.5 1.75C12.2279 1.75 15.25 4.77208 15.25 8.5C15.25 12.2279 12.2279 15.25 8.5 15.25C4.77379 15.2459 1.75413 12.2262 1.75 8.5Z"
      fill={Colors.secondary}
    />
  </Svg>
);

const HomeIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 19 19" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.75 8.83281V17.5C18.75 18.3284 18.0784 19 17.25 19H13.5C12.6716 19 12 18.3284 12 17.5V13.75C12 13.3358 11.6642 13 11.25 13H8.25C7.83579 13 7.5 13.3358 7.5 13.75V17.5C7.5 18.3284 6.82843 19 6 19H2.25C1.42157 19 0.75 18.3284 0.75 17.5V8.83281C0.749936 8.41309 0.92573 8.01254 1.23469 7.72844L8.73469 0.652188L8.745 0.641875C9.31719 0.121501 10.1912 0.121501 10.7634 0.641875C10.7666 0.645543 10.7701 0.648989 10.7738 0.652188L18.2738 7.72844C18.5796 8.01402 18.7522 8.41437 18.75 8.83281Z"
      fill={Colors.primary}
    />
  </Svg>
);

const CalendarIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 19 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.75 2H14.5V1.25C14.5 0.835786 14.1642 0.5 13.75 0.5C13.3358 0.5 13 0.835786 13 1.25V2H5.5V1.25C5.5 0.835786 5.16421 0.5 4.75 0.5C4.33579 0.5 4 0.835786 4 1.25V2H1.75C0.921573 2 0.25 2.67157 0.25 3.5V18.5C0.25 19.3284 0.921573 20 1.75 20H16.75C17.5784 20 18.25 19.3284 18.25 18.5V3.5C18.25 2.67157 17.5784 2 16.75 2ZM4 3.5V4.25C4 4.66421 4.33579 5 4.75 5C5.16421 5 5.5 4.66421 5.5 4.25V3.5H13V4.25C13 4.66421 13.3358 5 13.75 5C14.1642 5 14.5 4.66421 14.5 4.25V3.5H16.75V6.5H1.75V3.5H4ZM16.75 18.5H1.75V8H16.75V18.5ZM7.75 10.25V16.25C7.75 16.6642 7.41421 17 7 17C6.58579 17 6.25 16.6642 6.25 16.25V11.4631L5.83562 11.6713C5.4649 11.8566 5.01411 11.7063 4.82875 11.3356C4.64339 10.9649 4.79365 10.5141 5.16437 10.3287L6.66438 9.57875C6.89695 9.46237 7.17322 9.47478 7.39442 9.61155C7.61563 9.74832 7.75019 9.98993 7.75 10.25ZM13.2962 13.1047L11.5 15.5H13C13.4142 15.5 13.75 15.8358 13.75 16.25C13.75 16.6642 13.4142 17 13 17H10C9.71592 17 9.45622 16.8395 9.32918 16.5854C9.20214 16.3313 9.22955 16.0273 9.4 15.8L12.0981 12.2028C12.2653 11.9802 12.2955 11.6833 12.1764 11.4316C12.0573 11.1799 11.8086 11.0149 11.5304 11.003C11.2523 10.9912 10.9904 11.1344 10.8503 11.375C10.7202 11.6146 10.4703 11.7647 10.1976 11.7671C9.925 11.7694 9.67256 11.6236 9.53836 11.3863C9.40415 11.1489 9.40933 10.8574 9.55188 10.625C10.0612 9.74353 11.099 9.31391 12.0824 9.57743C13.0658 9.84095 13.7497 10.7319 13.75 11.75C13.7516 12.2391 13.5921 12.7152 13.2962 13.1047Z"
      fill={Colors.secondary}
    />
  </Svg>
);

const MessageIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 20 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.875 10C10.875 10.6213 10.3713 11.125 9.75 11.125C9.12868 11.125 8.625 10.6213 8.625 10C8.625 9.37868 9.12868 8.875 9.75 8.875C10.3713 8.875 10.875 9.37868 10.875 10ZM5.625 8.875C5.00368 8.875 4.5 9.37868 4.5 10C4.5 10.6213 5.00368 11.125 5.625 11.125C6.24632 11.125 6.75 10.6213 6.75 10C6.75 9.37868 6.24632 8.875 5.625 8.875ZM13.875 8.875C13.2537 8.875 12.75 9.37868 12.75 10C12.75 10.6213 13.2537 11.125 13.875 11.125C14.4963 11.125 15 10.6213 15 10C15 9.37868 14.4963 8.875 13.875 8.875ZM19.5 10C19.5007 13.424 17.7053 16.5975 14.77 18.3605C11.8347 20.1234 8.18978 20.2174 5.1675 18.6081L1.97531 19.6722C1.43626 19.852 0.841913 19.7117 0.44011 19.3099C0.0383081 18.9081 -0.101955 18.3137 0.0778124 17.7747L1.14188 14.5825C-0.76326 11.0006 -0.251258 6.61332 2.42747 3.56638C5.10619 0.519438 9.39177 -0.550316 13.1882 0.880322C16.9846 2.31096 19.4983 5.94298 19.5 10ZM18 10C17.999 6.53154 15.8287 3.43408 12.569 2.24891C9.30932 1.06374 5.65643 2.04399 3.42801 4.70188C1.19959 7.35977 0.871575 11.1276 2.60719 14.1306C2.7147 14.3167 2.73723 14.5399 2.66906 14.7437L1.5 18.25L5.00625 17.0809C5.08262 17.0549 5.16275 17.0416 5.24344 17.0416C5.37516 17.0418 5.5045 17.0767 5.61844 17.1428C8.17111 18.6197 11.318 18.6217 13.8725 17.148C16.4271 15.6743 18.0007 12.9491 18 10Z"
      fill={Colors.secondary}
    />
  </Svg>
);

const ProfileIcon = () => (
  <Svg width="24" height="24" viewBox="0 0 20 20" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M19.8988 17.875C18.4709 15.4066 16.2706 13.6366 13.7028 12.7975C16.3135 11.2433 17.5641 8.13638 16.7582 5.2069C15.9522 2.27741 13.2883 0.247449 10.25 0.247449C7.21167 0.247449 4.54779 2.27741 3.74182 5.2069C2.93585 8.13638 4.18645 11.2433 6.79719 12.7975C4.22938 13.6356 2.02906 15.4056 0.60125 17.875C0.458704 18.1074 0.453527 18.3989 0.587731 18.6363C0.721935 18.8736 0.974375 19.0194 1.24702 19.0171C1.51967 19.0147 1.76958 18.8646 1.89969 18.625C3.66594 15.5725 6.78781 13.75 10.25 13.75C13.7122 13.75 16.8341 15.5725 18.6003 18.625C18.7304 18.8646 18.9803 19.0147 19.253 19.0171C19.5256 19.0194 19.7781 18.8736 19.9123 18.6363C20.0465 18.3989 20.0413 18.1074 19.8988 17.875ZM5 7C5 4.1005 7.3505 1.75 10.25 1.75C13.1495 1.75 15.5 4.1005 15.5 7C15.5 9.8995 13.1495 12.25 10.25 12.25C7.35179 12.2469 5.0031 9.89821 5 7Z"
      fill={Colors.secondary}
    />
  </Svg>
);

// Component for service cards
const ServiceCard = ({ icon, title, onPress }) => (
  <TouchableOpacity
    style={styles.serviceCard}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <View style={styles.serviceIconContainer}>{icon}</View>
    <Text style={styles.serviceCardTitle}>{title}</Text>
  </TouchableOpacity>
);

// Component for content cards (webinars, videos)
const ContentCard = ({ title, author, imageSource, onPress }) => (
  <TouchableOpacity
    style={styles.contentCard}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <View style={styles.contentImageContainer}>
      <View style={styles.contentImagePlaceholder} />
    </View>
    <View style={styles.contentInfo}>
      <Text style={styles.contentTitle}>{title}</Text>
      <Text style={styles.contentAuthor}>{author}</Text>
    </View>
  </TouchableOpacity>
);

export default function DashboardScreen() {
  const router = useRouter();

  const handleServicePress = (service: string) => {
    console.log(`${service} pressed`);
  };

  const handleContentPress = (content: string) => {
    console.log(`${content} pressed`);
  };

  const handleEmergencyCall = () => {
    console.log("Emergency call pressed");
  };

  const handleNavigation = (tab: string) => {
    console.log(`Navigate to ${tab}`);
  };

  const handleSettings = () => {
    router.push("/landing");
  };

  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor={Colors.white} />
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerCenter}>
            <Text style={styles.logo}>AZMed+</Text>
          </View>
          <TouchableOpacity
            style={styles.settingsButton}
            onPress={handleSettings}
            activeOpacity={0.7}
          >
            <SettingsIcon />
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Search Bar */}
          <View style={styles.searchSection}>
            <View style={styles.searchContainer}>
              <View style={styles.searchIconContainer}>
                <SearchIcon />
              </View>
              <TextInput
                style={styles.searchInput}
                placeholder="How can we help you?"
                placeholderTextColor={Colors.secondary}
              />
            </View>
          </View>

          {/* Emergency Support Section */}
          <View style={styles.emergencySection}>
            <View style={styles.emergencyCard}>
              <LinearGradient
                colors={[Colors.gradientEnd, Colors.gradientStart]}
                style={styles.emergencyGradient}
              >
                <View style={styles.emergencyContent}>
                  <View style={styles.emergencyTextContainer}>
                    <Text style={styles.emergencyTitle}>
                      Emergency Support Line
                    </Text>
                    <Text style={styles.emergencySubtitle}>
                      7/24 Expert Support
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={styles.callButton}
                    onPress={handleEmergencyCall}
                    activeOpacity={0.8}
                  >
                    <Text style={styles.callButtonText}>Call</Text>
                  </TouchableOpacity>
                </View>
              </LinearGradient>
            </View>
          </View>

          {/* Services Grid */}
          <View style={styles.servicesSection}>
            <View style={styles.servicesGrid}>
              <View style={styles.servicesRow}>
                <ServiceCard
                  title="Online Consultation"
                  onPress={() => handleServicePress("Online Consultation")}
                />
                <ServiceCard
                  title="Cycle Tracking"
                  onPress={() => handleServicePress("Cycle Tracking")}
                />
              </View>
              <View style={styles.servicesRow}>
                <ServiceCard
                  title="Sexual Therapy"
                  onPress={() => handleServicePress("Sexual Therapy")}
                />
                <ServiceCard
                  title="Egg Reserve"
                  onPress={() => handleServicePress("Egg Reserve")}
                />
              </View>
              <View style={styles.servicesRow}>
                <ServiceCard
                  title="Menstrual Tracking"
                  onPress={() => handleServicePress("Menstrual Tracking")}
                />
                <ServiceCard
                  title="Academy"
                  onPress={() => handleServicePress("Academy")}
                />
              </View>
              <View style={styles.servicesRow}>
                <ServiceCard
                  title="Healthy Living"
                  onPress={() => handleServicePress("Healthy Living")}
                />
                <ServiceCard
                  title="Cancer Screening"
                  onPress={() => handleServicePress("Cancer Screening")}
                />
              </View>
            </View>
          </View>

          {/* Upcoming Events Section */}
          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>Upcoming Events</Text>
            <View style={styles.contentRow}>
              <ContentCard
                title="Webinar: Understanding PCOS"
                author="Dr. Amelia Carter"
                onPress={() => handleContentPress("PCOS Webinar")}
              />
              <ContentCard
                title="Webinar: Fertility Options"
                author="Dr. Sophia Bennett"
                onPress={() => handleContentPress("Fertility Webinar")}
              />
            </View>
          </View>

          {/* Most Watched Videos Section */}
          <View style={styles.contentSection}>
            <Text style={styles.sectionTitle}>Most Watched Videos</Text>
            <View style={styles.contentRow}>
              <ContentCard
                title="Understanding Endometriosis"
                author="Dr. Olivia Harper"
                onPress={() => handleContentPress("Endometriosis Video")}
              />
              <ContentCard
                title="Managing Menopause"
                author="Dr. Isabella Hayes"
                onPress={() => handleContentPress("Menopause Video")}
              />
            </View>
          </View>
        </ScrollView>

        {/* Bottom Navigation */}
        <View style={styles.bottomNavigation}>
          <TouchableOpacity
            style={styles.navItem}
            onPress={() => handleNavigation("Home")}
            activeOpacity={0.7}
          >
            <HomeIcon />
            <Text style={[styles.navText, styles.navTextActive]}>Home</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navItem}
            onPress={() => handleNavigation("Appointments")}
            activeOpacity={0.7}
          >
            <CalendarIcon />
            <Text style={styles.navText}>Appointments</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navItem}
            onPress={() => handleNavigation("Messages")}
            activeOpacity={0.7}
          >
            <MessageIcon />
            <Text style={styles.navText}>Messages</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.navItem}
            onPress={() => handleNavigation("Profile")}
            activeOpacity={0.7}
          >
            <ProfileIcon />
            <Text style={styles.navText}>Profile</Text>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },

  // Header Styles
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
    backgroundColor: Colors.white,
  },
  headerCenter: {
    flex: 1,
    alignItems: "center",
    paddingLeft: 48,
  },
  logo: {
    fontFamily: "Lexend",
    fontSize: 18,
    fontWeight: "700",
    lineHeight: 23,
    color: Colors.primary,
    textAlign: "center",
  },
  settingsButton: {
    width: 48,
    height: 48,
    justifyContent: "center",
    alignItems: "center",
  },

  // Search Section
  searchSection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    height: 48,
    backgroundColor: Colors.searchBackground,
    borderRadius: 12,
  },
  searchIconContainer: {
    paddingLeft: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  searchInput: {
    flex: 1,
    paddingHorizontal: 8,
    paddingVertical: 8,
    fontFamily: "Lexend",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.text,
  },

  // Emergency Section
  emergencySection: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  emergencyCard: {
    height: 262,
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: "#5A8A9C", // Fallback color
  },
  emergencyGradient: {
    flex: 1,
    justifyContent: "flex-end",
  },
  emergencyContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    padding: 16,
    paddingTop: 126,
  },
  emergencyTextContainer: {
    flex: 1,
    gap: 4,
  },
  emergencyTitle: {
    fontFamily: "Lexend",
    fontSize: 24,
    fontWeight: "700",
    lineHeight: 30,
    color: Colors.white,
  },
  emergencySubtitle: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.white,
  },
  callButton: {
    width: 84,
    height: 40,
    backgroundColor: Colors.accent,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  callButtonText: {
    fontFamily: "Lexend",
    fontSize: 14,
    fontWeight: "700",
    lineHeight: 21,
    color: Colors.white,
    textAlign: "center",
  },

  // Services Section
  servicesSection: {
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  servicesGrid: {
    gap: 12,
  },
  servicesRow: {
    flexDirection: "row",
    gap: 12,
  },
  serviceCard: {
    flex: 1,
    padding: 16,
    backgroundColor: Colors.cardBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    alignItems: "center",
    gap: 12,
  },
  serviceIconContainer: {
    width: 24,
    height: 24,
  },
  serviceCardTitle: {
    fontFamily: "Lexend",
    fontSize: 13,
    fontWeight: "700",
    lineHeight: 20,
    color: Colors.text,
    textAlign: "center",
  },

  // Content Sections
  contentSection: {
    paddingTop: 20,
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  sectionTitle: {
    fontFamily: "Lexend",
    fontSize: 22,
    fontWeight: "700",
    lineHeight: 28,
    color: Colors.text,
    marginBottom: 16,
  },
  contentRow: {
    flexDirection: "row",
    gap: 12,
  },
  contentCard: {
    flex: 1,
    gap: 16,
  },
  contentImageContainer: {
    height: 97,
  },
  contentImagePlaceholder: {
    flex: 1,
    backgroundColor: "#5A8A9C",
    borderRadius: 12,
  },
  contentInfo: {
    gap: 8,
  },
  contentTitle: {
    fontFamily: "Lexend",
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    color: Colors.text,
  },
  contentAuthor: {
    fontFamily: "Lexend",
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 21,
    color: Colors.secondary,
  },

  // Bottom Navigation
  bottomNavigation: {
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
    backgroundColor: Colors.white,
    borderTopWidth: 1,
    borderTopColor: Colors.bottomBorder,
  },
  navItem: {
    flex: 1,
    alignItems: "center",
    gap: 4,
    paddingVertical: 8,
  },
  navText: {
    fontFamily: "Lexend",
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    color: Colors.secondary,
    textAlign: "center",
  },
  navTextActive: {
    color: Colors.primary,
  },
});

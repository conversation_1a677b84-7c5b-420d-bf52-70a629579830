# Hello World Expo App 👋

This is a clean [Expo](https://expo.dev) project that displays "Hello World".

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
   npx expo start --tunnel
   ```

In the output, you'll find options to open the app in a

- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go)

The app simply displays "Hello World" text in the center of the screen.

## Project Structure

- `app/index.tsx` - Main screen that displays "Hello World"
- `app/_layout.tsx` - Root layout configuration
- `app.json` - Expo configuration
- `package.json` - Dependencies and scripts

## Learn more

To learn more about developing with Expo:

- [Expo documentation](https://docs.expo.dev/)
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/)